<template>
  <section class="service-rate-table-read-only">
    <!-- Loading State -->
    <div
      v-if="isLoadingServiceRate || isLoadingVariations"
      class="text-center py-4"
    >
      <v-progress-circular
        indeterminate
        color="primary"
        size="40"
      ></v-progress-circular>
      <p class="mt-2 text-muted">Loading service rates and variations...</p>
    </div>

    <!-- No Data State -->
    <v-alert
      v-else-if="!serviceRate || !serviceRate.rateTableItems.length"
      :value="true"
      color="info"
      icon="info"
      class="mb-3"
    >
      No service rates are currently configured.
    </v-alert>

    <!-- Rate Type Navigation Buttons -->
    <v-flex v-else class="button-group">
      <v-tooltip
        v-for="rateType in availableRateTypes"
        :key="rateType.rateTypeId"
        bottom
        :disabled="true"
      >
        <template v-slot:activator="{ on }">
          <span v-on="on">
            <v-btn
              :class="{
                'v-btn--active': selectedRateTypeId === rateType.rateTypeId,
              }"
              flat
              @click="selectedRateTypeId = rateType.rateTypeId"
            >
              <span class="px-2">
                <strong>{{ rateType.longName.toUpperCase() }} RATES</strong>
              </span>
            </v-btn>
          </span>
        </template>
      </v-tooltip>
    </v-flex>

    <!-- tables -->
    <div
      v-if="
        !isLoadingServiceRate &&
        !isLoadingVariations &&
        serviceRate &&
        serviceRate.rateTableItems.length > 0
      "
    >
      <!-- Time Rate Table -->
      <div
        v-if="
          selectedRateTypeId === JobRateType.TIME && timeRateItems.length > 0
        "
      >
        <v-data-table
          class="default-table-dark client-invoice-accounting-table gd-dark-theme"
          :headers="timeHeaders"
          :items="timeRateItems"
          hide-actions
          :rows-per-page-items="[10, 20]"
        >
          <template v-slot:items="slotProps">
            <tr>
              <td>{{ getServiceName(slotProps.item.serviceTypeId) }}</td>
              <td>
                <v-tooltip
                  v-if="slotProps.item.rateTypeObject.rate > 0"
                  bottom
                  :color="getRateVariationColor(slotProps.item)"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <span
                      v-bind="attrs"
                      v-on="on"
                      class="rate-text"
                      :class="
                        slotProps.item.rateTypeObject.rate > 0 &&
                        showRateVariationsHighlight
                          ? `${getRateVariationColor(slotProps.item)}`
                          : ''
                      "
                    >
                      {{ formatTimeRateWithVariation(slotProps.item) }}
                    </span>
                  </template>
                  <span>{{ getRateTooltip(slotProps.item, 'time') }}</span>
                </v-tooltip>
                <span v-else class="rate-text">
                  {{ formatTimeRateWithVariation(slotProps.item) }}
                </span>
              </td>
              <td>{{ formatMinCharge(slotProps.item.rateTypeObject) }}</td>
              <td>
                {{ formatChargeIncrement(slotProps.item.rateTypeObject) }}
              </td>
              <td>
                {{
                  returnGraceShortName(slotProps.item.rateTypeObject.graceType)
                }}
              </td>
              <td>
                <v-tooltip
                  v-if="slotProps.item.rateTypeObject.standbyRate > 0"
                  bottom
                  :color="getRateVariationColor(slotProps.item)"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <span
                      v-bind="attrs"
                      v-on="on"
                      class="rate-text"
                      :class="
                        slotProps.item.rateTypeObject.standbyRate > 0 &&
                        showRateVariationsHighlight
                          ? `${getRateVariationColor(slotProps.item)}`
                          : ''
                      "
                    >
                      {{ formatStandbyRateWithVariation(slotProps.item) }}
                    </span>
                  </template>
                  <span>{{ getRateTooltip(slotProps.item, 'standby') }}</span>
                </v-tooltip>
                <span v-else class="rate-text">
                  {{ formatStandbyRateWithVariation(slotProps.item) }}
                </span>
              </td>
              <td>
                {{
                  formatFuelSurcharge(
                    slotProps.item.rateTypeObject.appliedFuelSurchargeId,
                  )
                }}
              </td>
            </tr>
          </template>
        </v-data-table>
      </div>

      <!-- Zone Rate Table -->
      <div
        v-if="
          selectedRateTypeId === JobRateType.ZONE && zoneRateItems.length > 0
        "
      >
        <v-data-table
          class="default-table-dark client-invoice-accounting-table gd-dark-theme"
          :headers="zoneHeaders"
          :items="zoneRateTableData"
          hide-actions
          :rows-per-page-items="[10, 20]"
        >
          <template v-slot:items="slotProps">
            <tr>
              <td>{{ slotProps.item.serviceTypeName }}</td>
              <td>
                <v-tooltip
                  v-if="slotProps.item.rate > 0"
                  bottom
                  :color="getRateVariationColor(slotProps.item)"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <span
                      v-bind="attrs"
                      v-on="on"
                      class="rate-text"
                      :class="
                        slotProps.item.rateTypeObject.rate > 0 &&
                        showRateVariationsHighlight
                          ? `${getRateVariationColor(slotProps.item)}`
                          : ''
                      "
                    >
                      {{ slotProps.item.zoneRate }}
                    </span>
                  </template>
                  <span>{{ getRateTooltip(slotProps.item, 'zone') }}</span>
                </v-tooltip>
                <span v-else class="rate-text">
                  {{ slotProps.item.zoneRate }}
                </span>
              </td>
              <td>{{ slotProps.item.pickupFlagfall }}</td>
              <td>{{ slotProps.item.dropoffFlagfall }}</td>
              <td>{{ slotProps.item.percentage }}</td>
              <td>{{ slotProps.item.demurrageRate }}</td>
              <td>{{ slotProps.item.appliedFuelSurcharge }}</td>
            </tr>
          </template>
        </v-data-table>
      </div>

      <!-- Distance Rate Table -->
      <div
        v-if="
          selectedRateTypeId === JobRateType.DISTANCE &&
          distanceRateItems.length > 0
        "
      >
        <v-data-table
          class="default-table-dark client-invoice-accounting-table gd-dark-theme"
          :headers="distanceHeaders"
          :items="distanceRateItems"
          hide-actions
          :rows-per-page-items="[10, 20]"
        >
          <template v-slot:items="slotProps">
            <tr>
              <td>{{ getServiceName(slotProps.item.serviceTypeId) }}</td>
              <td>
                <v-tooltip
                  v-if="slotProps.item.rateTypeObject.baseFreightCharge > 0"
                  bottom
                  :color="getRateVariationColor(slotProps.item)"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <span
                      v-bind="attrs"
                      v-on="on"
                      class="rate-text"
                      :class="
                        slotProps.item.rateTypeObject.baseFreightCharge > 0 &&
                        showRateVariationsHighlight
                          ? `${getRateVariationColor(slotProps.item)}`
                          : ''
                      "
                    >
                      {{ formatBaseFreightWithVariation(slotProps.item) }}
                    </span>
                  </template>
                  <span>{{ getRateTooltip(slotProps.item, 'distance') }}</span>
                </v-tooltip>
                <span v-else class="rate-text">
                  {{ formatBaseFreightWithVariation(slotProps.item) }}
                </span>
              </td>
              <td>{{ formatIncrement(slotProps.item.rateTypeObject) }}</td>
              <td>
                <v-tooltip
                  v-if="slotProps.item.rateTypeObject.minCharge > 0"
                  bottom
                  :color="getRateVariationColor(slotProps.item)"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <span
                      v-bind="attrs"
                      v-on="on"
                      class="rate-text"
                      :class="
                        slotProps.item.rateTypeObject.minCharge > 0 &&
                        showRateVariationsHighlight
                          ? `${getRateVariationColor(slotProps.item)}`
                          : ''
                      "
                    >
                      {{ formatMinimumChargeWithVariation(slotProps.item) }}
                    </span>
                  </template>
                  <span>{{ getMinimumChargeTooltip(slotProps.item) }}</span>
                </v-tooltip>
                <span v-else class="rate-text">
                  {{ formatMinimumChargeWithVariation(slotProps.item) }}
                </span>
              </td>
              <td>{{ formatRange(slotProps.item.rateTypeObject) }}</td>
              <td>
                {{
                  formatFuelSurcharge(
                    slotProps.item.rateTypeObject.appliedFuelSurchargeId,
                  )
                }}
              </td>
            </tr>
          </template>
        </v-data-table>
      </div>

      <!-- Point-to-Point Rate Table -->
      <div
        v-if="
          selectedRateTypeId === JobRateType.POINT_TO_POINT &&
          pointToPointRateItems.length > 0
        "
      >
        <v-data-table
          class="default-table-dark client-invoice-accounting-table gd-dark-theme"
          :headers="pointToPointHeaders"
          :items="pointToPointRateTableData"
          hide-actions
          :rows-per-page-items="[10, 20]"
        >
          <template v-slot:items="slotProps">
            <tr>
              <td>{{ slotProps.item.serviceTypeName }}</td>
              <td>{{ slotProps.item.fromAddress }}</td>
              <td>{{ slotProps.item.toAddress }}</td>
              <td>
                <v-tooltip
                  v-if="slotProps.item.baseRate > 0"
                  bottom
                  :color="getRateVariationColor(slotProps.item)"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <span
                      v-bind="attrs"
                      v-on="on"
                      class="rate-text"
                      :class="
                        slotProps.item.rateTypeObject.baseRate > 0 &&
                        showRateVariationsHighlight
                          ? `${getRateVariationColor(slotProps.item)}`
                          : ''
                      "
                    >
                      {{ slotProps.item.rate }}
                    </span>
                  </template>
                  <span>{{
                    getRateTooltip(slotProps.item, 'pointToPoint')
                  }}</span>
                </v-tooltip>
                <span v-else class="rate-text">
                  {{ slotProps.item.rate }}
                </span>
              </td>
              <td>{{ slotProps.item.demurrageRate }}</td>
              <td>{{ slotProps.item.appliedFuelSurcharge }}</td>
            </tr>
          </template>
        </v-data-table>
      </div>

      <!-- Unit Rate Table -->
      <div
        v-if="
          selectedRateTypeId === JobRateType.UNIT && unitRateItems.length > 0
        "
      >
        <v-data-table
          class="default-table-dark client-invoice-accounting-table gd-dark-theme"
          :headers="unitHeaders"
          :items="unitRateTableData"
          hide-actions
          :rows-per-page-items="[10, 20]"
        >
          <template v-slot:items="slotProps">
            <tr>
              <td>{{ slotProps.item.unitTypeName }}</td>
              <td>{{ slotProps.item.zoneName }}</td>
              <td>
                <v-tooltip
                  v-if="hasUnitRateGreaterThanZero(slotProps.item)"
                  bottom
                  :color="getUnitRateVariationColor(slotProps.item)"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <span
                      v-bind="attrs"
                      v-on="on"
                      class="rate-text"
                      :class="
                        hasUnitRateGreaterThanZero(slotProps.item) &&
                        showRateVariationsHighlight
                          ? `${getRateVariationColor(slotProps.item)}`
                          : ''
                      "
                    >
                      {{ formatUnitRateWithVariation(slotProps.item) }}
                    </span>
                  </template>
                  <span>{{ getRateTooltip(slotProps.item, 'unit') }}</span>
                </v-tooltip>
                <span v-else class="rate-text">
                  {{ formatUnitRateWithVariation(slotProps.item) }}
                </span>
              </td>
              <td>
                {{ slotProps.item.unitAmountMultiplier }}
              </td>
              <td>{{ slotProps.item.appliedFuelSurcharge }}</td>
              <td>
                <v-tooltip
                  v-if="hasUnitDemurrageRateGreaterThanZero(slotProps.item)"
                  bottom
                  :color="getUnitRateVariationColor(slotProps.item)"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <span
                      v-bind="attrs"
                      v-on="on"
                      class="rate-text"
                      :class="getUnitRateVariationColor(slotProps.item)"
                    >
                      {{ formatUnitDemurrageWithVariation(slotProps.item) }}
                    </span>
                  </template>
                  <span>{{ getUnitDemurrageTooltip(slotProps.item) }}</span>
                </v-tooltip>
                <span v-else class="rate-text">
                  {{ formatUnitDemurrageWithVariation(slotProps.item) }}
                </span>
              </td>
            </tr>
          </template>
        </v-data-table>
      </div>
    </div>
    <!-- End Rate Tables Container -->
  </section>
</template>

<script setup lang="ts">
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { getServiceTypeById } from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { CurrentClientServiceRateResponse } from '@/interface-models/Client/CurrentClientServiceRateResponse';
import { returnGraceShortName } from '@/interface-models/Generic/ServiceTypes/GraceTypes';
import { rateMultipliers } from '@/interface-models/Generic/ServiceTypes/RateMultipliers';
import serviceTypeRates, {
  JobRateType,
  ServiceTypeRates,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import applicableFuelSurcharges from '@/interface-models/ServiceRates/FuelSurcharges/applicableFuelSurcharges';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import { returnReadableChargeBasisName } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/ChargeBasis';
import DistanceRateType from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/DistanceRateType';
import { returnReadableRateBracketTypeName } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/RateBracketType';
import PointToPointRateType from '@/interface-models/ServiceRates/ServiceTypes/PointToPointServiceRate/PointToPointRateType';
import TimeRateType from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import ZoneRateType from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import { useServiceRateVariationsStore } from '@/store/modules/ServiceRateVariationsStore';
import moment from 'moment-timezone';
import { computed, ComputedRef, onMounted, ref, Ref } from 'vue';

// Type definitions for table data items
interface ZoneRateTableItem {
  index: number;
  serviceTypeName: string;
  serviceTypeId: number | undefined;
  zoneName: string;
  zoneRate: string;
  baseZoneRate: number;
  pickupFlagfall: string;
  dropoffFlagfall: string;
  demurrageGrace: string;
  demurrageRate: string;
  appliedFuelSurcharge: string;
  percentage: string;
  isClient: boolean;
  demurrageFuelSurchargeApplies: string;
  originalService: RateTableItems;
}

interface PointToPointRateTableItem {
  index: number;
  serviceTypeName: string;
  serviceTypeId: number | undefined;
  fromAddress: string;
  toAddress: string;
  rate: string;
  baseRate: number;
  demurrageRate: string;
  appliedFuelSurcharge: string;
  percentage: string;
  isClient: boolean;
  demurrageFuelSurchargeApplies: string;
  originalService: RateTableItems;
}

interface UnitRateTableItem {
  index: number;
  serviceTypeName: string;
  serviceTypeId: number | undefined;
  unitTypeName: string;
  zoneName: string;
  unitRate: string;
  unitAmountMultiplier: string;
  demurrageRate: string;
  appliedFuelSurcharge: string;
  percentage: string;
  isClient: boolean;
  demurrageFuelSurchargeApplies: string;
  originalService: RateTableItems;
  unitRateObject: UnitRate;
}

const props = withDefaults(
  defineProps<{
    clientId: string;
    searchDate?: number;
    showRateVariationsHighlight?: boolean;
  }>(),
  {
    searchDate: Date.now(),
    showRateVariationsHighlight: true,
  },
);

const serviceRateStore = useServiceRateStore();
const serviceRateVariationsStore = useServiceRateVariationsStore();

const selectedRateTypeId: Ref<JobRateType> = ref(JobRateType.TIME);

const availableRateTypes: ComputedRef<ServiceTypeRates[]> = computed(() => {
  if (!serviceRate.value || !serviceRate.value.rateTableItems.length) {
    return [];
  }
  // Get unique rate type IDs
  const rateTypeIdsInData = new Set(
    serviceRate.value.rateTableItems.map((item) => item.rateTypeId),
  );
  // Filter serviceTypeRates to only include rate types that have data
  return serviceTypeRates.filter(
    (rateType) => !rateType.adhoc && rateTypeIdsInData.has(rateType.rateTypeId),
  );
});

// service rates and rate variations
const serviceRate: Ref<ClientServiceRate | null> = ref(null);
const isLoadingServiceRate: Ref<boolean> = ref(false);
const rateVariations: Ref<ClientServiceRateVariations[]> = ref([]);
const isLoadingVariations: Ref<boolean> = ref(false);

// Load service rates for the client
async function loadServiceRate(): Promise<void> {
  if (!props.clientId) {
    return;
  }

  isLoadingServiceRate.value = true;
  try {
    const searchDate = props.searchDate || Date.now();
    const response: CurrentClientServiceRateResponse | null =
      await serviceRateStore.getMergedClientServiceRates(
        props.clientId,
        searchDate,
      );
    if (response) {
      serviceRate.value = response.clientServiceRate;
    }
  } catch (error) {
    console.error('Error loading service rates:', error);
    serviceRate.value = null;
  } finally {
    isLoadingServiceRate.value = false;
  }
}

// Helper function to get service name
function getServiceName(serviceTypeId: number | undefined): string {
  if (!serviceTypeId) {
    return '-';
  }
  const serviceType = getServiceTypeById(serviceTypeId);
  return serviceType ? serviceType.optionSelectName : '-';
}

// Time Rate Items and Headers
const timeRateItems: ComputedRef<RateTableItems[]> = computed(() => {
  if (!serviceRate.value) {
    return [];
  }
  return serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.TIME,
  );
});

const timeHeaders: ComputedRef<TableHeader[]> = computed(() => [
  {
    text: 'Service Type',
    align: 'left',
    value: 'serviceType',
    sortable: false,
  },
  { text: 'Rate', align: 'left', value: 'rate', sortable: false },
  { text: 'Min Charge', align: 'left', value: 'minCharge', sortable: false },
  {
    text: 'Charge Increment',
    align: 'left',
    value: 'chargeIncrement',
    sortable: false,
  },
  { text: 'Grace', align: 'left', value: 'grace', sortable: false },
  {
    text: 'Standby Rate',
    align: 'left',
    value: 'standbyRate',
    sortable: false,
  },
  {
    text: 'Fuel Surcharge',
    align: 'left',
    value: 'fuelSurcharge',
    sortable: false,
  },
]);

// Zone Rate Items and Headers
const zoneRateItems: ComputedRef<RateTableItems[]> = computed(() => {
  if (!serviceRate.value) {
    return [];
  }
  return serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.ZONE,
  );
});

const zoneHeaders: ComputedRef<TableHeader[]> = computed(() => [
  {
    text: 'Service Type',
    align: 'left',
    value: 'serviceType',
    sortable: false,
  },
  { text: 'Zone Rate', align: 'left', value: 'zoneRate', sortable: false },
  {
    text: 'Pickup Flag Fall',
    align: 'left',
    value: 'pickupFlagfall',
    sortable: false,
  },
  {
    text: 'Drop-off Flag Fall',
    align: 'left',
    value: 'dropoffFlagfall',
    sortable: false,
  },
  {
    text: 'Percentage',
    align: 'left',
    value: 'percentage',
    sortable: false,
  },
  { text: 'Demurrage', align: 'left', value: 'demurrage', sortable: false },
  {
    text: 'Fuel Surcharge',
    align: 'left',
    value: 'fuelSurcharge',
    sortable: false,
  },
]);

// Distance Rate Items and Headers
const distanceRateItems: ComputedRef<RateTableItems[]> = computed(() => {
  if (!serviceRate.value) {
    return [];
  }
  return serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.DISTANCE,
  );
});

const distanceHeaders: ComputedRef<TableHeader[]> = computed(() => [
  {
    text: 'Service Type',
    align: 'left',
    value: 'serviceType',
    sortable: false,
  },
  {
    text: 'Base Freight Rate',
    align: 'left',
    value: 'baseFreight',
    sortable: false,
  },
  { text: 'Increment', align: 'left', value: 'increment', sortable: false },
  {
    text: 'Minimum Charge',
    align: 'left',
    value: 'minimumCharge',
    sortable: false,
  },
  { text: 'Range', align: 'left', value: 'range', sortable: false },
  {
    text: 'Fuel Surcharge',
    align: 'left',
    value: 'fuelSurcharge',
    sortable: false,
  },
]);

// Point-to-Point Rate Items and Headers
const pointToPointRateItems: ComputedRef<RateTableItems[]> = computed(() => {
  if (!serviceRate.value) {
    return [];
  }
  return serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.POINT_TO_POINT,
  );
});

const pointToPointHeaders: ComputedRef<TableHeader[]> = computed(() => [
  {
    text: 'Service Type',
    align: 'left',
    value: 'serviceType',
    sortable: false,
  },
  { text: 'From', align: 'left', value: 'from', sortable: false },
  { text: 'To', align: 'left', value: 'to', sortable: false },
  { text: 'Rate', align: 'left', value: 'rate', sortable: false },
  {
    text: 'Percentage',
    align: 'left',
    value: 'percentage',
    sortable: false,
  },
  { text: 'Demurrage', align: 'left', value: 'demurrage', sortable: false },
  {
    text: 'Fuel Surcharge',
    align: 'left',
    value: 'fuelSurcharge',
    sortable: false,
  },
]);

// Unit Rate Items and Headers
const unitRateItems: ComputedRef<RateTableItems[]> = computed(() => {
  if (!serviceRate.value) {
    return [];
  }
  return serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.UNIT,
  );
});

const unitHeaders: ComputedRef<TableHeader[]> = computed(() => [
  { text: 'Unit Type', align: 'left', value: 'unitType', sortable: false },
  { text: 'Zone', align: 'left', value: 'zone', sortable: false },
  { text: 'Rate', align: 'left', value: 'rate', sortable: false },
  {
    text: 'Per Amount',
    align: 'left',
    value: 'perAmount',
    sortable: false,
  },
  {
    text: 'Fuel Surcharge',
    align: 'left',
    value: 'fuelSurcharge',
    sortable: false,
  },
  { text: 'Demurrage', align: 'left', value: 'demurrage', sortable: false },
]);

// Computed properties for table data
const zoneRateTableData = computed(() => {
  if (!serviceRate.value) {
    return [];
  }

  const tableData: ZoneRateTableItem[] = [];

  for (const service of zoneRateItems.value) {
    const serviceTypeName = getServiceName(service.serviceTypeId);
    const zoneRates: ZoneRateType[] = service.rateTypeObject as ZoneRateType[];

    const zoneTableRate = zoneRates.map((zone: ZoneRateType, index: number) => {
      const appliedFuelSurcharge = applicableFuelSurcharges.find(
        (x) => zone.appliedFuelSurchargeId === x.id,
      );

      // const multiplier = rateMultipliers.find(
      //   (rateMultiplier) =>
      //     rateMultiplier.id === zone.demurrage.incrementMultiplier,
      // );

      const demurrageGraceAsMinutes = moment
        .duration(zone.demurrage.graceTimeInMilliseconds)
        .asMinutes();

      return {
        index,
        serviceTypeName,
        serviceTypeId: service.serviceTypeId,
        zoneName: zone.zoneName,
        zoneRate: formatZoneRateWithVariation(service, zone.rate),
        baseZoneRate: zone.rate, // Store base rate for tooltip
        pickupFlagfall: `${zone.additionalPickUpFlagFall} mins`,
        dropoffFlagfall: `${zone.additionalDropOffFlagFall} mins`,
        demurrageGrace: !demurrageGraceAsMinutes
          ? 'None'
          : moment.duration(demurrageGraceAsMinutes, 'minutes').humanize(),
        demurrageRate: `Apply: $${DisplayCurrencyValue(
          zone.demurrage.rate,
        )}/hr with grace period`,
        appliedFuelSurcharge: appliedFuelSurcharge
          ? appliedFuelSurcharge.shortName
          : '-',
        percentage: `${zone.percentage}%`,
        isClient: true,
        demurrageFuelSurchargeApplies: zone.demurrage
          .demurrageFuelSurchargeApplies
          ? 'Apply'
          : "Don't Apply",
        originalService: service,
      };
    });

    tableData.push(...zoneTableRate);
  }

  return tableData;
});

const pointToPointRateTableData = computed(() => {
  if (!serviceRate.value) {
    return [];
  }

  const tableData: PointToPointRateTableItem[] = [];

  for (const service of pointToPointRateItems.value) {
    const serviceTypeName = getServiceName(service.serviceTypeId);
    const p2pRates: PointToPointRateType[] =
      service.rateTypeObject as PointToPointRateType[];

    const p2pTableRate = p2pRates.map(
      (p2p: PointToPointRateType, index: number) => {
        const appliedFuelSurcharge = applicableFuelSurcharges.find(
          (x) => p2p.appliedFuelSurchargeId === x.id,
        );

        // const multiplier = rateMultipliers.find(
        //   (rateMultiplier) =>
        //     rateMultiplier.id === p2p.demurrage.incrementMultiplier,
        // );

        // const demurrageGraceAsMinutes = moment
        //   .duration(p2p.demurrage.graceTimeInMilliseconds)
        //   .asMinutes();

        return {
          index,
          serviceTypeName,
          serviceTypeId: service.serviceTypeId,
          fromAddress: p2p.fromAddressReference,
          toAddress: p2p.toAddressReference,
          rate: formatZoneRateWithVariation(service, p2p.rate),
          baseRate: p2p.rate, // Store base rate for tooltip
          demurrageRate: `Apply: $${DisplayCurrencyValue(
            p2p.demurrage.rate,
          )}/hr with grace period`,
          appliedFuelSurcharge: appliedFuelSurcharge
            ? appliedFuelSurcharge.shortName
            : '-',
          percentage: `${p2p.percentage}%`,
          isClient: true,
          demurrageFuelSurchargeApplies: p2p.demurrage
            .demurrageFuelSurchargeApplies
            ? 'Apply'
            : "Don't Apply",
          originalService: service,
        };
      },
    );

    tableData.push(...p2pTableRate);
  }

  return tableData;
});

const unitRateTableData = computed(() => {
  if (!serviceRate.value) {
    return [];
  }

  const tableData: UnitRateTableItem[] = [];

  for (const service of unitRateItems.value) {
    const serviceTypeName = getServiceName(service.serviceTypeId);
    const unitRates: UnitRate[] = service.rateTypeObject as UnitRate[];

    const unitTableRate = unitRates.map((unit: UnitRate, index: number) => {
      const appliedFuelSurcharge = applicableFuelSurcharges.find(
        (x) => unit.appliedFuelSurchargeId === x.id,
      );

      // const multiplier = rateMultipliers.find(
      //   (rateMultiplier) =>
      //     rateMultiplier.id === unit.demurrage.incrementMultiplier,
      // );

      return {
        index,
        serviceTypeName,
        serviceTypeId: service.serviceTypeId,
        unitTypeName: unit.unitTypeName,
        zoneName: unit.zoneName,
        unitRate: unit.unitRanges
          .map((range) => `$${DisplayCurrencyValue(range.unitRate)}`)
          .join(', '),
        unitAmountMultiplier: unit.unitRanges
          .map((range) => range.unitAmountMultiplier)
          .join(', '),
        demurrageRate: `Apply: $${DisplayCurrencyValue(
          unit.demurrage.rate,
        )}/hr with grace period`,
        appliedFuelSurcharge: appliedFuelSurcharge
          ? appliedFuelSurcharge.shortName
          : '-',
        percentage: `${unit.fleetAssetPercentage}%`,
        isClient: true,
        demurrageFuelSurchargeApplies: unit.demurrage
          .demurrageFuelSurchargeApplies
          ? 'Apply'
          : "Don't Apply",
        // Add original service data for rate variations
        originalService: service,
        unitRateObject: unit,
      };
    });

    tableData.push(...unitTableRate);
  }

  return tableData;
});

function formatMinCharge(rateObj: TimeRateType): string {
  const multiplier = rateMultipliers.find(
    (m) => m.id === rateObj.minChargeMultiplier,
  );
  return `${rateObj.minCharge} ${multiplier?.longName || 'mins'}`;
}

function formatChargeIncrement(rateObj: TimeRateType): string {
  const multiplier = rateMultipliers.find(
    (m) => m.id === rateObj.chargeIncrementMultiplier,
  );
  return `${rateObj.chargeIncrement} ${multiplier?.longName || 'mins'}`;
}

function formatFuelSurcharge(fuelSurchargeId: number): string {
  const fuelSurcharge = applicableFuelSurcharges.find(
    (f) => f.id === fuelSurchargeId,
  );
  return fuelSurcharge?.shortName || 'Apply';
}

// Helper function to get rate variation percentage for an item
function getRateVariationPercentage(item: RateTableItems): number | null {
  if (!rateVariations.value.length) {
    return null;
  }

  const matchingVariation = rateVariations.value.find((variation) => {
    const serviceTypeMatches =
      variation.serviceTypeId === null ||
      variation.serviceTypeId === item.serviceTypeId;

    const rateTypeMatches =
      variation.rateTypeId === null ||
      variation.rateTypeId === selectedRateTypeId.value;

    const now = Date.now();
    const validFrom = variation.validFromDate || 0;
    const validTo = variation.validToDate || Number.MAX_SAFE_INTEGER;
    const isActive = now >= validFrom && now <= validTo;

    return serviceTypeMatches && rateTypeMatches && isActive;
  });

  if (!matchingVariation) {
    return null;
  }

  const clientAdjustment = matchingVariation.clientAdjustmentPercentage;
  const fleetAssetAdjustment = matchingVariation.fleetAssetAdjustmentPercentage;
  return clientAdjustment !== null ? clientAdjustment : fleetAssetAdjustment;
}

// Helper function to calculate adjusted rate
function calculateAdjustedRate(
  baseRate: number,
  variationPercentage: number | null,
): number {
  if (variationPercentage === null) {
    return baseRate;
  }
  return baseRate * (1 + variationPercentage / 100);
}

// Enhanced formatting functions with rate variations
function formatTimeRateWithVariation(item: RateTableItems): string {
  const rateObj: TimeRateType = item.rateTypeObject as TimeRateType;
  const multiplier = rateMultipliers.find(
    (m) => m.id === rateObj.rateMultiplier,
  );
  const unit = multiplier?.shortName || 'hr';

  const variationPercentage = getRateVariationPercentage(item);
  const adjustedRate = calculateAdjustedRate(rateObj.rate, variationPercentage);

  let result = `$${DisplayCurrencyValue(adjustedRate)}/${unit}`;

  return result;
}

function formatStandbyRateWithVariation(item: RateTableItems): string {
  const rateObj: TimeRateType = item.rateTypeObject as TimeRateType;
  const multiplier = rateMultipliers.find(
    (m) => m.id === rateObj.standbyMultiplier,
  );
  const unit = multiplier?.shortName || 'hr';

  const variationPercentage = getRateVariationPercentage(item);
  const adjustedRate = calculateAdjustedRate(
    rateObj.standbyRate,
    variationPercentage,
  );

  let result = `$${DisplayCurrencyValue(adjustedRate)}/${unit}`;

  return result;
}

// Enhanced formatting function for Zone Rate
function formatZoneRateWithVariation(
  item: RateTableItems,
  baseRate: number,
): string {
  const variationPercentage = getRateVariationPercentage(item);
  const adjustedRate = calculateAdjustedRate(baseRate, variationPercentage);

  let result = `$${DisplayCurrencyValue(adjustedRate)}/hr`;

  return result;
}

// Consolidated tooltip function for all rate types
function getRateTooltip(
  item: any,
  rateType: 'time' | 'standby' | 'zone' | 'pointToPoint' | 'distance' | 'unit',
): string {
  const serviceItem = item.originalService || item;
  const variationPercentage = getRateVariationPercentage(serviceItem);
  const sign =
    variationPercentage !== null && variationPercentage >= 0 ? '+' : '';

  switch (rateType) {
    case 'time': {
      const rateObj: TimeRateType = serviceItem.rateTypeObject as TimeRateType;
      const multiplier = rateMultipliers.find(
        (m) => m.id === rateObj.rateMultiplier,
      );
      const unit = multiplier?.shortName || 'hr';

      if (variationPercentage === null) {
        return `Base rate: $${DisplayCurrencyValue(rateObj.rate)}/${unit}`;
      }

      const adjustedRate = calculateAdjustedRate(
        rateObj.rate,
        variationPercentage,
      );
      return `$${DisplayCurrencyValue(
        rateObj.rate,
      )}/${unit} ${sign}${variationPercentage.toFixed(
        2,
      )}% = $${DisplayCurrencyValue(adjustedRate)}/${unit}`;
    }

    case 'standby': {
      const rateObj: TimeRateType = serviceItem.rateTypeObject as TimeRateType;
      const multiplier = rateMultipliers.find(
        (m) => m.id === rateObj.standbyMultiplier,
      );
      const unit = multiplier?.shortName || 'hr';

      if (variationPercentage === null) {
        return `Base standby rate: $${DisplayCurrencyValue(
          rateObj.standbyRate,
        )}/${unit}`;
      }

      const adjustedRate = calculateAdjustedRate(
        rateObj.standbyRate,
        variationPercentage,
      );
      return `$${DisplayCurrencyValue(
        rateObj.standbyRate,
      )}/${unit} ${sign}${variationPercentage.toFixed(
        2,
      )}% = $${DisplayCurrencyValue(adjustedRate)}/${unit}`;
    }

    case 'zone': {
      const baseRate = item.baseZoneRate;

      if (variationPercentage === null) {
        return `Base zone rate: $${DisplayCurrencyValue(baseRate)}/hr`;
      }

      const adjustedRate = calculateAdjustedRate(baseRate, variationPercentage);
      return `$${DisplayCurrencyValue(
        baseRate,
      )}/hr ${sign}${variationPercentage.toFixed(2)}% = $${DisplayCurrencyValue(
        adjustedRate,
      )}/hr`;
    }

    case 'pointToPoint': {
      const baseRate = item.baseRate;

      if (variationPercentage === null) {
        return `Base rate: $${DisplayCurrencyValue(baseRate)}/hr`;
      }

      const adjustedRate = calculateAdjustedRate(baseRate, variationPercentage);
      return `$${DisplayCurrencyValue(
        baseRate,
      )}/hr ${sign}${variationPercentage.toFixed(2)}% = $${DisplayCurrencyValue(
        adjustedRate,
      )}/hr`;
    }

    case 'distance': {
      const rateObj: DistanceRateType =
        serviceItem.rateTypeObject as DistanceRateType;
      const baseFreight = rateObj.baseFreightCharge || 0;

      if (variationPercentage === null) {
        return `Base freight charge: $${DisplayCurrencyValue(baseFreight)}`;
      }

      const adjustedRate = calculateAdjustedRate(
        baseFreight,
        variationPercentage,
      );
      return `$${DisplayCurrencyValue(
        baseFreight,
      )} ${sign}${variationPercentage.toFixed(2)}% = $${DisplayCurrencyValue(
        adjustedRate,
      )}`;
    }

    case 'unit': {
      // For unit rates, we need to access the original service data
      const originalService = item.originalService;
      const unitRateObject: UnitRate = item.unitRateObject;

      if (!originalService || !unitRateObject) {
        return 'Unit rate information not available';
      }

      // Get the variation percentage from the original service
      const serviceVariationPercentage =
        getRateVariationPercentage(originalService);

      if (serviceVariationPercentage === null) {
        return `Base unit rates: ${unitRateObject.unitRanges
          .map((range) => `$${DisplayCurrencyValue(range.unitRate)}`)
          .join(', ')}`;
      }

      const adjustedRates = unitRateObject.unitRanges.map((range) => {
        const adjustedRate = calculateAdjustedRate(
          range.unitRate,
          serviceVariationPercentage,
        );
        return `$${DisplayCurrencyValue(
          range.unitRate,
        )} ${sign}${serviceVariationPercentage.toFixed(
          2,
        )}% = $${DisplayCurrencyValue(adjustedRate)}`;
      });

      return adjustedRates.join(', ');
    }

    default:
      return '';
  }
}

// Helper function to get tooltip background color based on rate variation
function getRateVariationColor(item: RateTableItems): string {
  const variationPercentage = getRateVariationPercentage(item);

  if (variationPercentage === null) {
    return 'neutral'; // Default grey for no variation
  }

  if (variationPercentage > 0) {
    return 'positive'; // Green for positive variation (rate increase)
  } else {
    return 'negative'; // Red for negative variation (rate decrease)
  }
}

// Enhanced formatting function for Distance Rate with variation
function formatBaseFreightWithVariation(item: RateTableItems): string {
  const rateObj: DistanceRateType = item.rateTypeObject as DistanceRateType;
  const baseFreight = rateObj.baseFreightCharge || 0;
  const variationPercentage = getRateVariationPercentage(item);
  const adjustedRate = calculateAdjustedRate(baseFreight, variationPercentage);

  return `$${DisplayCurrencyValue(adjustedRate)}`;
}

// Enhanced formatting function for Unit Rate with variation
function formatUnitRateWithVariation(item: UnitRateTableItem): string {
  const originalService = item.originalService;
  const unitRateObject: UnitRate = item.unitRateObject;

  if (!originalService || !unitRateObject) {
    return item.unitRate; // Fallback to original display
  }

  // Get the variation percentage from the original service
  const variationPercentage = getRateVariationPercentage(originalService);

  const adjustedRates = unitRateObject.unitRanges.map((range) => {
    const adjustedRate = calculateAdjustedRate(
      range.unitRate,
      variationPercentage,
    );
    return `$${DisplayCurrencyValue(adjustedRate)}`;
  });

  return adjustedRates.join(', ');
}

// Helper function to get rate variation color for unit rates
function getUnitRateVariationColor(item: UnitRateTableItem): string {
  const originalService = item.originalService;

  if (!originalService) {
    return 'neutral';
  }

  return getRateVariationColor(originalService);
}

// Helper function to check if unit rate has any rates > 0
function hasUnitRateGreaterThanZero(item: UnitRateTableItem): boolean {
  const unitRateObject: UnitRate = item.unitRateObject;

  if (!unitRateObject || !unitRateObject.unitRanges) {
    return false;
  }

  return unitRateObject.unitRanges.some((range) => range.unitRate > 0);
}

// Helper function to check if unit demurrage rate > 0
function hasUnitDemurrageRateGreaterThanZero(item: UnitRateTableItem): boolean {
  const unitRateObject: UnitRate = item.unitRateObject;

  if (!unitRateObject || !unitRateObject.demurrage) {
    return false;
  }

  return unitRateObject.demurrage.rate > 0;
}

// Enhanced formatting function for Unit Rate demurrage with variation
function formatUnitDemurrageWithVariation(item: UnitRateTableItem): string {
  const originalService = item.originalService;
  const unitRateObject: UnitRate = item.unitRateObject;

  if (!originalService || !unitRateObject) {
    return item.demurrageRate; // Fallback to original display
  }

  // Get the variation percentage from the original service
  const variationPercentage = getRateVariationPercentage(originalService);
  const demurrageRate = unitRateObject.demurrage.rate;
  const adjustedRate = calculateAdjustedRate(
    demurrageRate,
    variationPercentage,
  );

  return `Apply: $${DisplayCurrencyValue(adjustedRate)}/hr with grace period`;
}

// Tooltip function for unit demurrage rate with variation
function getUnitDemurrageTooltip(item: UnitRateTableItem): string {
  const originalService = item.originalService;
  const unitRateObject: UnitRate = item.unitRateObject;

  if (!originalService || !unitRateObject) {
    return 'Demurrage rate information not available';
  }

  const demurrageRate = unitRateObject.demurrage.rate;
  const variationPercentage = getRateVariationPercentage(originalService);
  const sign =
    variationPercentage !== null && variationPercentage >= 0 ? '+' : '';

  if (variationPercentage === null) {
    return `Base demurrage rate: $${DisplayCurrencyValue(demurrageRate)}/hr`;
  }

  const adjustedRate = calculateAdjustedRate(
    demurrageRate,
    variationPercentage,
  );
  return `$${DisplayCurrencyValue(
    demurrageRate,
  )}/hr ${sign}${variationPercentage.toFixed(2)}% = $${DisplayCurrencyValue(
    adjustedRate,
  )}/hr`;
}

function formatIncrement(rateObj: DistanceRateType): string {
  return `${returnReadableChargeBasisName(
    rateObj.chargeBasis,
  )} (${returnReadableRateBracketTypeName(rateObj.rateBracketType)})`;
}

// Enhanced formatting function for Distance Rate minimum charge with variation
function formatMinimumChargeWithVariation(item: RateTableItems): string {
  const rateObj: DistanceRateType = item.rateTypeObject as DistanceRateType;
  const minCharge = rateObj.minCharge || 0;
  const variationPercentage = getRateVariationPercentage(item);
  const adjustedRate = calculateAdjustedRate(minCharge, variationPercentage);

  return `$${DisplayCurrencyValue(adjustedRate)}`;
}

// Tooltip function for minimum charge with variation
function getMinimumChargeTooltip(item: RateTableItems): string {
  const rateObj: DistanceRateType = item.rateTypeObject as DistanceRateType;
  const minCharge = rateObj.minCharge || 0;
  const variationPercentage = getRateVariationPercentage(item);
  const sign =
    variationPercentage !== null && variationPercentage >= 0 ? '+' : '';

  if (variationPercentage === null) {
    return `Base minimum charge: $${DisplayCurrencyValue(minCharge)}`;
  }

  const adjustedRate = calculateAdjustedRate(minCharge, variationPercentage);
  return `$${DisplayCurrencyValue(
    minCharge,
  )} ${sign}${variationPercentage.toFixed(2)}% = $${DisplayCurrencyValue(
    adjustedRate,
  )}`;
}

function formatRange(rateObj: DistanceRateType): string {
  if (rateObj.rates && rateObj.rates.length > 0) {
    const firstRange = rateObj.rates[0];
    const lastRange = rateObj.rates[rateObj.rates.length - 1];
    return `${firstRange.bracketMin}km - ${
      lastRange.bracketMax === -1 ? '∞' : lastRange.bracketMax + 'km'
    }`;
  }
  return '-';
}

// Load rate variations for the client
async function loadRateVariations(): Promise<void> {
  if (!props.clientId) {
    return;
  }

  isLoadingVariations.value = true;
  try {
    const searchDate = Date.now(); // Use current date for active variations
    const variations =
      await serviceRateVariationsStore.getServiceRateVariationsByClient(
        props.clientId,
        searchDate,
      );
    rateVariations.value = variations || [];
  } catch (error) {
    console.error('Error loading rate variations:', error);
    rateVariations.value = [];
  } finally {
    isLoadingVariations.value = false;
  }
}

// Load service rates and rate variations when component mounts
onMounted(async () => {
  await Promise.all([loadServiceRate(), loadRateVariations()]);

  // Auto-select the first available rate type if the current selection is not available
  if (availableRateTypes.value.length > 0) {
    const currentRateTypeAvailable = availableRateTypes.value.some(
      (rateType) => rateType.rateTypeId === selectedRateTypeId.value,
    );

    if (!currentRateTypeAvailable) {
      selectedRateTypeId.value = availableRateTypes.value[0].rateTypeId;
    }
  }
});
</script>

<style scoped lang="scss">
.service-rate-table-read-only {
  .rate-text {
    font-weight: bold;
    cursor: pointer;

    &.neutral {
      color: var(--text-color);
    }

    &.positive {
      color: $success;
    }
    &.negative {
      color: $error;
    }
  }
}

.positive {
  border-color: $success;
}
.negative {
  border-color: $error;
}

// Loading state styling
.text-center {
  .text-muted {
    color: #6c757d;
    font-size: 14px;
  }
}

.btn-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  flex-direction: row-reverse;
}
</style>
